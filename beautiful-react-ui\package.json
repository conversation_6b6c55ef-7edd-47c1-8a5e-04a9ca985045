{"name": "innovaid-for-sece", "version": "1.0.0", "private": true, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "server": "nodemon server/server.js", "dev": "concurrently \"npm run server\" \"npm start\"", "client": "npm start"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@hookform/resolvers": "^3.3.2", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "axios": "^1.6.0", "bcryptjs": "^3.0.2", "concurrently": "^9.2.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.18.2", "firebase": "^10.7.1", "framer-motion": "^10.16.4", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.292.0", "mongoose": "^8.16.4", "nodemon": "^3.1.10", "react": "^17.0.2", "react-dom": "^17.0.2", "react-hook-form": "^7.47.0", "react-router-dom": "^6.17.0", "react-scripts": "^5.0.1", "react-toastify": "^9.1.3", "styled-components": "^6.1.1", "typescript": "^4.1.3", "yup": "^1.3.3"}, "devDependencies": {"@types/react": "^17.0.2", "@types/react-dom": "^17.0.2", "@types/react-router-dom": "^5.3.3", "@types/styled-components": "^5.1.29", "@types/yup": "^0.32.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}